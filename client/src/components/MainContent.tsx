import { useState, useEffect, useRef } from "react";
import { useActiveAccount, useActiveWalletChain } from "thirdweb/react";
import { getChainById } from "@/lib/chainConfig";
import { toast } from "@/hooks/use-toast";
import { useNebulaChat } from "@/hooks/use-nebula-chat";
import WelcomePage from "@/components/WelcomePage";
import ChatInterface from "@/components/ChatInterface";

type CurrentView = "welcome" | "chat";

const MainContent = () => {
  const isSendingRef = useRef(false);

  // Current view state
  const [currentView, setCurrentView] = useState<CurrentView>("welcome");

  // Use our custom Nebula chat hook
  const {
    activeChat,
    messages,
    isLoading,
    sendStreamingMessage,
    createChat,
    setActiveChat,
  } = useNebulaChat();

  // ThirdWeb v5 hooks
  const account = useActiveAccount();
  const activeChain = useActiveWalletChain();

  // Extract values from v5 hooks
  const address = account?.address;
  const chainId = activeChain?.id;

  // Get current chainId (default to Ethereum if not connected)
  const currentChainId = chainId || 1;
  const currentChain = getChainById(currentChainId);

  // Handle chain changes
  useEffect(() => {
    if (activeChain && activeChat) {
      // Show toast notification on network change
      // toast({
      //   title: "Network Changed",
      //   description: `Switched to ${currentChain?.name || "Unknown Network"}`,
      //   variant: "default",
      // });
    }
  }, [activeChain?.id, activeChat, currentChain?.name]);

  // Switch to welcome view when there's no active chat
  useEffect(() => {
    if (!activeChat && messages.length === 0) {
      setCurrentView("welcome");
    }
  }, [activeChat, messages.length]);

  // Handle send message with streaming
  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Prevent duplicate calls using ref
    if (isSendingRef.current || isLoading.sendMessage) {
      console.log("Already sending message, ignoring duplicate call");
      return;
    }

    if (!address) {
      toast({
        title: "Wallet Required",
        description: "Please connect your wallet to start chatting",
        variant: "destructive",
      });
      return;
    }

    // Set flag to prevent duplicate calls
    isSendingRef.current = true;

    try {
      // Switch to chat view immediately
      setCurrentView("chat");

      // If we don't have an active chat, create one first
      if (!activeChat) {
        const newChat = await createChat("Web3AI Assistant");
        if (newChat) {
          // Now send the streaming message to the new chat using the chat ID
          await sendStreamingMessage(content, newChat.id);
        }
      } else {
        await sendStreamingMessage(content);
      }
    } finally {
      // Reset flag when done
      isSendingRef.current = false;
    }
  };

  // Handle back to welcome page
  const handleBackToWelcome = () => {
    setActiveChat(null);
    setCurrentView("welcome");
  };

  return (
    <main
      className="flex-1 flex flex-col h-full overflow-hidden bg-background min-w-0"
      style={{ marginTop: "64px" }}
    >
      {/* Responsive container with proper mobile spacing */}
      <div
        className="w-full mx-auto flex flex-col h-full px-4 sm:px-6 lg:px-8 py-2 sm:py-4"
        style={{ maxWidth: "55rem" }}
      >
        {currentView === "welcome" ? (
          <WelcomePage
            onSendMessage={handleSendMessage}
            isLoading={isLoading.sendMessage}
          />
        ) : (
          <ChatInterface
            messages={messages}
            onSendMessage={handleSendMessage}
            onBackToWelcome={handleBackToWelcome}
            isLoading={isLoading.sendMessage}
          />
        )}
      </div>
    </main>
  );
};

export default MainContent;
