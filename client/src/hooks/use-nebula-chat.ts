import { useState, useEffect, useCallback } from "react";
import { useActiveAccount, useActiveWalletChain } from "thirdweb/react";
import {
  NebulaChat,
  NebulaMessage,
  getNebulaChats,
  getNebulaChatMessages,
  sendNebulaMessage,
  createNebulaChat,
} from "@/lib/chatClient";
import { toast } from "@/hooks/use-toast";
import { getChainById } from "@/lib/chainConfig";
import { useNebulaSession } from "@/hooks/use-nebula-session";

export function useNebulaChat() {
  const account = useActiveAccount();
  const activeChain = useActiveWalletChain();
  const { sessionId, ensureSession } = useNebulaSession();

  // Extract address and chainId from v5 hooks
  const address = account?.address;
  const chainId = activeChain?.id;

  const [chats, setChats] = useState<NebulaChat[]>([]);
  const [activeChat, setActiveChat] = useState<NebulaChat | null>(null);
  const [messages, setMessagesInternal] = useState<NebulaMessage[]>([]);

  // Wrapper to debug all setMessages calls
  const setMessages = (
    newMessages: NebulaMessage[] | ((prev: NebulaMessage[]) => NebulaMessage[])
  ) => {
    if (typeof newMessages === "function") {
      setMessagesInternal((prev) => {
        const result = newMessages(prev);
        console.log("setMessages (function):", {
          prevLength: prev.length,
          newLength: result.length,
          stackTrace: new Error().stack?.split("\n")[2]?.trim(),
        });
        return result;
      });
    } else {
      console.log("setMessages (direct):", {
        newLength: newMessages.length,
        stackTrace: new Error().stack?.split("\n")[2]?.trim(),
      });
      setMessagesInternal(newMessages);
    }
  };
  const [isLoading, setIsLoading] = useState({
    chats: false,
    messages: false,
    sendMessage: false,
    createChat: false,
  });

  // Track when we've just completed a streaming operation to prevent fetchMessages
  const [justCompletedStreaming, setJustCompletedStreaming] = useState(false);

  // Get current chainId (default to Ethereum if not connected)
  const currentChainId = chainId || 1;
  const currentChain = getChainById(currentChainId);

  // Fetch all chats
  const fetchChats = useCallback(async () => {
    if (!address) return; // Don't fetch chats if no wallet connected

    console.log("fetchChats: Starting to fetch chats for address:", address);
    setIsLoading((prev) => ({ ...prev, chats: true }));

    try {
      const fetchedChats = await getNebulaChats(address);
      console.log(
        "fetchChats: Successfully fetched",
        fetchedChats.length,
        "chats"
      );
      setChats(fetchedChats);
    } catch (error: any) {
      console.error("Error fetching chats:", error);
      toast({
        title: "Error fetching chats",
        description: error.message || "Failed to fetch chats",
        variant: "destructive",
      });
    } finally {
      console.log("fetchChats: Setting loading to false");
      setIsLoading((prev) => ({ ...prev, chats: false }));
    }
  }, [address]);

  // Fetch messages for a specific chat
  const fetchMessages = useCallback(
    async (chatId: number) => {
      if (!chatId || !address) return;

      setIsLoading((prev) => ({ ...prev, messages: true }));

      try {
        const fetchedMessages = await getNebulaChatMessages(chatId, address);
        console.log("fetchMessages result:", {
          chatId,
          fetchedMessagesLength: fetchedMessages.length,
          fetchedMessages: fetchedMessages.slice(0, 2), // Show first 2 for debugging
        });
        console.log("setMessages called with fetchedMessages");
        setMessages(fetchedMessages);
      } catch (error: any) {
        console.error("Error fetching messages:", error);
        toast({
          title: "Error fetching messages",
          description: error.message || "Failed to fetch messages",
          variant: "destructive",
        });
      } finally {
        setIsLoading((prev) => ({ ...prev, messages: false }));
      }
    },
    [address]
  );

  // Send a message to the active chat or specified chat ID
  const sendMessage = useCallback(
    async (content: string, chatId?: number) => {
      const targetChatId = chatId || activeChat?.id;
      if (!targetChatId || !content.trim() || !address) return;

      // Create optimistic user message to show immediately in UI
      const optimisticUserMessage: NebulaMessage = {
        id: Date.now(), // Temporary ID
        chatId: targetChatId,
        role: "user",
        content: content.trim(),
        timestamp: new Date().toISOString(),
        metadata: {
          chainId: currentChainId.toString(),
          sender: address,
        },
      };

      // Immediately add user message to UI (optimistic update)
      setMessages((prev) => [...prev, optimisticUserMessage]);

      setIsLoading((prev) => ({ ...prev, sendMessage: true }));

      try {
        // Ensure we have a session before sending the message
        const currentSessionId = await ensureSession();

        // Add blockchain context to message metadata
        const metadata = {
          chainId: currentChainId,
          sender: address,
        };

        // Send the message with session ID, wallet address, and chain context
        const sentMessage = await sendNebulaMessage(
          targetChatId,
          content,
          address,
          metadata,
          currentSessionId || undefined,
          chainId || 1, // Pass numeric chainId
          account
        );

        // Fetch updated messages to get both user and assistant responses
        const updatedMessages = await getNebulaChatMessages(
          targetChatId,
          address
        );
        setMessages(updatedMessages);

        return sentMessage;
      } catch (error: any) {
        console.error("Error sending message:", error);

        // Remove the optimistic message on error
        setMessages((prev) =>
          prev.filter((msg) => msg.id !== optimisticUserMessage.id)
        );

        toast({
          title: "Error sending message",
          description: error.message || "Failed to send message",
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading((prev) => ({ ...prev, sendMessage: false }));
      }
    },
    [activeChat, address, currentChainId, ensureSession, chainId, account]
  );

  // Send a streaming message to the active chat or specified chat ID
  const sendStreamingMessage = useCallback(
    async (
      content: string,
      chatId?: number,
      onChunk?: (chunk: string) => void
    ) => {
      const targetChatId = chatId || activeChat?.id;
      if (!targetChatId || !content.trim() || !address) return;

      // Create optimistic user message to show immediately in UI
      const optimisticUserMessage: NebulaMessage = {
        id: Date.now(), // Temporary ID
        chatId: targetChatId,
        role: "user",
        content: content.trim(),
        timestamp: new Date().toISOString(),
        metadata: {
          chainId: currentChainId.toString(),
          sender: address,
        },
      };

      // Immediately add user message to UI (optimistic update)
      setMessages((prev) => [...prev, optimisticUserMessage]);

      // Create optimistic assistant message for streaming
      const optimisticAssistantMessage: NebulaMessage = {
        id: Date.now() + 1, // Temporary ID
        chatId: targetChatId,
        role: "assistant",
        content: "",
        timestamp: new Date().toISOString(),
        metadata: {
          chainId: currentChainId.toString(),
          isStreaming: true,
        },
      };

      // Add empty assistant message for streaming
      setMessages((prev) => [...prev, optimisticAssistantMessage]);

      setIsLoading((prev) => ({ ...prev, sendMessage: true }));

      try {
        // Ensure we have a session before sending the message
        const currentSessionId = await ensureSession();

        // Import the streaming function
        const { sendNebulaMessageStream } = await import("@/lib/nebulaApi");

        // Create context filter with current wallet and chain
        const contextFilter = {
          walletAddresses: address ? [address] : undefined,
          chainIds: chainId ? [chainId] : undefined,
        };

        let streamingContent = "";

        await sendNebulaMessageStream(
          content,
          contextFilter,
          currentSessionId || undefined,
          (chunk: string) => {
            streamingContent += chunk;
            // Update the assistant message with streaming content
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === optimisticAssistantMessage.id
                  ? { ...msg, content: streamingContent }
                  : msg
              )
            );
            // Call the external onChunk callback with the accumulated content
            onChunk?.(streamingContent);
          },
          (metadata: any) => {
            // Debug: Log streaming completion metadata
            console.log("Streaming completion metadata:", {
              metadata,
              hasActions: !!metadata.actions,
              actionsLength: metadata.actions?.length,
              hasTransactions: !!metadata.transactions,
              transactionsLength: metadata.transactions?.length,
            });

            // Generate fallback transaction if no actions from Nebula API
            let finalActions = metadata.actions || [];

            // Check if this looks like a transaction request and no actions were provided
            const messageContent = streamingContent.toLowerCase();
            const isTransactionRequest =
              messageContent.includes("send") &&
              (messageContent.includes("amoy") ||
                messageContent.includes("eth") ||
                messageContent.includes("matic"));

            if (
              isTransactionRequest &&
              (!finalActions || finalActions.length === 0)
            ) {
              console.log(
                "Generating fallback transaction action for:",
                content
              );

              // Extract amount and address from the original user message
              const amountMatch = content.match(
                /(\d+(?:\.\d+)?)\s*(amoy|eth|matic)/i
              );
              const addressMatch = content.match(/0x[a-fA-F0-9]{40}/);

              if (amountMatch && addressMatch) {
                const amount = amountMatch[1];
                const token = amountMatch[2].toLowerCase();
                const toAddress = addressMatch[0];

                // Convert amount to wei (assuming 18 decimals)
                const amountInWei = (parseFloat(amount) * 1e18).toString();

                finalActions = [
                  {
                    session_id: metadata.session_id || "fallback",
                    request_id: metadata.request_id || "fallback",
                    type: "sign_transaction",
                    source: "fallback",
                    data: JSON.stringify({
                      to: toAddress,
                      value: amountInWei,
                      chainId: chainId || 80002, // Default to Amoy testnet
                      data: "0x",
                    }),
                  },
                ];

                console.log("Generated fallback action:", finalActions[0]);
              }
            }

            // Handle completion - finalize the message
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === optimisticAssistantMessage.id
                  ? {
                      ...msg,
                      content: streamingContent,
                      metadata: {
                        ...msg.metadata,
                        isStreaming: false,
                        session_id: metadata.session_id,
                        request_id: metadata.request_id,
                        blockchainData: {
                          actions: finalActions,
                          transactions: metadata.transactions || [],
                          session_id: metadata.session_id,
                          request_id: metadata.request_id,
                        },
                      },
                    }
                  : msg
              )
            );
            // Set flag to prevent fetchMessages after streaming completes
            setJustCompletedStreaming(true);
          },
          (error: string) => {
            console.error("Streaming error:", error);
            toast({
              title: "Streaming Error",
              description: error,
              variant: "destructive",
            });
            // Remove the optimistic assistant message on error
            setMessages((prev) =>
              prev.filter((msg) => msg.id !== optimisticAssistantMessage.id)
            );
          }
        );

        return {
          userMessage: optimisticUserMessage,
          assistantMessage: optimisticAssistantMessage,
        };
      } catch (error: any) {
        console.error("Error sending streaming message:", error);

        // Remove the optimistic messages on error
        setMessages((prev) =>
          prev.filter(
            (msg) =>
              msg.id !== optimisticUserMessage.id &&
              msg.id !== optimisticAssistantMessage.id
          )
        );

        toast({
          title: "Error sending message",
          description: error.message || "Failed to send streaming message",
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading((prev) => ({ ...prev, sendMessage: false }));
      }
    },
    [activeChat, address, currentChainId, ensureSession, chainId]
  );

  // Create a new chat
  const createChat = useCallback(
    async (title: string = "New Chat") => {
      if (!address) {
        throw new Error("Wallet address is required to create a chat");
      }

      setIsLoading((prev) => ({ ...prev, createChat: true }));

      try {
        // Add blockchain context to chat metadata
        const metadata = {
          chainId: currentChainId,
          network: currentChain?.name || "Ethereum",
          createdBy: address,
        };

        // Create the chat with wallet address
        const newChat = await createNebulaChat(title, address, metadata);

        // Update chats in state
        setChats((prev) => [newChat, ...prev]);

        // Set as active chat
        setActiveChat(newChat);

        // toast({
        //   title: "Chat created",
        //   description: "New chat thread created successfully",
        // });

        return newChat;
      } catch (error: any) {
        console.error("Error creating chat:", error);
        toast({
          title: "Error creating chat",
          description: error.message || "Failed to create chat",
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading((prev) => ({ ...prev, createChat: false }));
      }
    },
    [address, currentChainId, currentChain]
  );

  // Set active chat
  const setActiveChatAndFetchMessages = useCallback(
    (chat: NebulaChat | null) => {
      setActiveChat(chat);
      if (chat) {
        fetchMessages(chat.id);
      } else {
        // Clear messages when no chat is active - only if there are messages to clear
        if (messages.length > 0) {
          setMessages([]);
        }
      }
    },
    [fetchMessages, messages.length]
  );

  // Fetch chats when component mounts or address changes
  useEffect(() => {
    if (address) {
      fetchChats();
    } else {
      // Clear chats when wallet is disconnected
      setChats([]);
      setActiveChat(null);
      setMessages([]);
    }
  }, [address, fetchChats]);

  // Fetch messages when active chat changes (but not when sending a message)
  useEffect(() => {
    if (activeChat && !isLoading.sendMessage) {
      console.log("useEffect: fetchMessages triggered", {
        activeChatId: activeChat.id,
        isLoadingSendMessage: isLoading.sendMessage,
        currentMessagesLength: messages.length,
        justCompletedStreaming,
      });

      // If we just completed streaming, don't fetch - we already have the messages
      if (justCompletedStreaming) {
        console.log("Skipping fetchMessages due to justCompletedStreaming");
        setJustCompletedStreaming(false);
        return;
      }

      fetchMessages(activeChat.id);
    }
  }, [activeChat, fetchMessages, isLoading.sendMessage]);

  return {
    chats,
    messages,
    activeChat,
    isLoading,
    fetchChats,
    fetchMessages,
    sendMessage,
    sendStreamingMessage,
    createChat,
    setActiveChat: setActiveChatAndFetchMessages,
  };
}
